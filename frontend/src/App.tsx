import React from "react";
import { Routes, Route } from "react-router-dom";
import Layout from "./components/Layout";
import Dashboard from "./pages/Dashboard";
import Login from "./pages/Login";
import Settings from "./pages/Settings";
import Logs from "./pages/Logs";
import { AuthProvider } from "./contexts/AuthContext";
import { ProxyProvider } from "./contexts/ProxyContext";
import { useTokenRefresh } from "./hooks/useTokenRefresh";

function AppContent() {
  // Use the token refresh hook to automatically refresh tokens
  useTokenRefresh();

  return (
    <div className="App">
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/" element={<Layout />}>
          <Route index element={<Dashboard />} />
          <Route path="settings" element={<Settings />} />
          <Route path="logs" element={<Logs />} />
        </Route>
      </Routes>
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <ProxyProvider>
        <AppContent />
      </ProxyProvider>
    </AuthProvider>
  );
}

export default App;