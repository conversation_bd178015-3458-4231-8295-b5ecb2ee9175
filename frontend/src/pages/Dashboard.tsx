import React, { useState, useEffect } from "react";
import { Play, Square, Wifi, Upload, Download, Clock, Settings, MapPin, Zap, RefreshCw } from "lucide-react";
import { useProxy } from "../contexts/ProxyContext";
import { useAuth } from "../contexts/AuthContext";
import { invoke } from "@tauri-apps/api/core";
import clsx from "clsx";

interface ProxyNode {
  id: string;
  name: string;
  location: string;
  latency: number;
  available: boolean;
}

interface ProxyMode {
  id: string;
  name: string;
  description: string;
}

const Dashboard: React.FC = () => {
  const { isConnected, isConnecting, stats, error, connect, disconnect } = useProxy();
  const { isAuthenticated } = useAuth();
  const [proxyMode, setProxyMode] = useState<string>("global");
  const [selectedNode, setSelectedNode] = useState<string>("direct");
  const [nodes, setNodes] = useState<ProxyNode[]>([]);
  const [loadingNodes, setLoadingNodes] = useState(false);
  const [testingLatency, setTestingLatency] = useState<Set<string>>(new Set());

  const proxyModes: ProxyMode[] = [
    { id: "global", name: "Global Mode", description: "Route all traffic through VPN" },
    { id: "pac", name: "PAC Mode", description: "Use proxy auto-config rules" },
    { id: "manual", name: "Manual Mode", description: "Configure proxy manually" }
  ];

  // Load proxy nodes on component mount
  useEffect(() => {
    loadProxyNodes();
  }, [isAuthenticated]);

  const loadProxyNodes = async () => {
    if (!isAuthenticated) return;
    
    setLoadingNodes(true);
    try {
      const result = await invoke<{ nodes: ProxyNode[]; selected: string }>("get_proxy_nodes");
      setNodes(result.nodes);
      setSelectedNode(result.selected);
    } catch (err) {
      console.error("Failed to load proxy nodes:", err);
    } finally {
      setLoadingNodes(false);
    }
  };

  const testNodeLatency = async (nodeId: string) => {
    setTestingLatency(prev => new Set(prev).add(nodeId));
    
    try {
      // Simulate latency testing - in real implementation this would ping the server
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      // Update node latency with random value for demo
      const newLatency = Math.floor(Math.random() * 200) + 10;
      setNodes(prev => prev.map(node => 
        node.id === nodeId ? { ...node, latency: newLatency } : node
      ));
    } catch (err) {
      console.error("Failed to test latency:", err);
    } finally {
      setTestingLatency(prev => {
        const newSet = new Set(prev);
        newSet.delete(nodeId);
        return newSet;
      });
    }
  };

  const selectNode = async (nodeId: string) => {
    try {
      await invoke<string>("select_proxy_node", { nodeId });
      setSelectedNode(nodeId);
    } catch (err) {
      console.error("Failed to select node:", err);
    }
  };

  const handleModeChange = (mode: string) => {
    setProxyMode(mode);
    // In real implementation, this would update the proxy configuration
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const handleToggleConnection = () => {
    if (isConnected) {
      disconnect();
    } else {
      connect();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Monitor your VPN connection and statistics</p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        </div>
      )}

      {/* Connection Status Card */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div
                className={clsx(
                  "w-4 h-4 rounded-full mr-3",
                  isConnected ? "bg-green-500" : "bg-red-500"
                )}
              />
              <div>
                <h2 className="text-lg font-medium text-gray-900">
                  VPN Status
                </h2>
                <p className="text-sm text-gray-500">
                  {isConnected ? "Connected and secure" : "Disconnected"}
                </p>
                {selectedNode !== "direct" && (
                  <p className="text-xs text-blue-600">
                    via {nodes.find(n => n.id === selectedNode)?.name || selectedNode}
                  </p>
                )}
              </div>
            </div>
            
            <button
              onClick={handleToggleConnection}
              disabled={isConnecting}
              className={clsx(
                "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",
                isConnected
                  ? "bg-red-600 hover:bg-red-700 focus:ring-red-500"
                  : "bg-green-600 hover:bg-green-700 focus:ring-green-500"
              )}
            >
              {isConnecting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              ) : isConnected ? (
                <Square className="w-4 h-4 mr-2" />
              ) : (
                <Play className="w-4 h-4 mr-2" />
              )}
              {isConnecting
                ? "Connecting..."
                : isConnected
                ? "Disconnect"
                : "Connect"
              }
            </button>
          </div>
        </div>
      </div>

      {/* Proxy Mode Selection */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            Proxy Mode
          </h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {proxyModes.map((mode) => (
              <button
                key={mode.id}
                onClick={() => handleModeChange(mode.id)}
                className={clsx(
                  "p-4 text-left rounded-lg border-2 transition-colors",
                  proxyMode === mode.id
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                )}
              >
                <h4 className="font-medium text-gray-900">{mode.name}</h4>
                <p className="text-sm text-gray-500 mt-1">{mode.description}</p>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Node Selection */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <MapPin className="w-5 h-5 mr-2" />
              Server Nodes
            </h3>
            <button
              onClick={loadProxyNodes}
              disabled={loadingNodes}
              className="inline-flex items-center px-3 py-1 text-sm font-medium text-blue-600 hover:text-blue-500 disabled:opacity-50"
            >
              <RefreshCw className={clsx("w-4 h-4 mr-1", loadingNodes && "animate-spin")} />
              Refresh
            </button>
          </div>
        </div>
        <div className="p-6">
          <div className="space-y-3">
            {nodes.map((node) => (
              <div
                key={node.id}
                className={clsx(
                  "flex items-center justify-between p-4 rounded-lg border-2 transition-colors cursor-pointer",
                  selectedNode === node.id
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300",
                  !node.available && "opacity-50 cursor-not-allowed"
                )}
                onClick={() => node.available && selectNode(node.id)}
              >
                <div className="flex items-center">
                  <div
                    className={clsx(
                      "w-3 h-3 rounded-full mr-3",
                      node.available ? "bg-green-500" : "bg-red-500"
                    )}
                  />
                  <div>
                    <h4 className="font-medium text-gray-900">{node.name}</h4>
                    <p className="text-sm text-gray-500">{node.location}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {node.latency}ms
                    </div>
                    <div className="text-xs text-gray-500">latency</div>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      testNodeLatency(node.id);
                    }}
                    disabled={testingLatency.has(node.id)}
                    className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                  >
                    <Zap className={clsx(
                      "w-4 h-4",
                      testingLatency.has(node.id) && "animate-pulse"
                    )} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Real-time Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Upload Statistics */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Upload className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Upload
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {formatBytes(stats.bytesUploaded)}
                  </dd>
                  <dd className="text-xs text-blue-600">
                    {formatBytes(stats.uploadSpeed || 0)}/s
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Download Statistics */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Download className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Download
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {formatBytes(stats.bytesDownloaded)}
                  </dd>
                  <dd className="text-xs text-green-600">
                    {formatBytes(stats.downloadSpeed || 0)}/s
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Connection Time */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Uptime
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {formatTime(stats.connectionTime)}
                  </dd>
                  <dd className="text-xs text-purple-600">
                    {isConnected ? "Connected" : "Offline"}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Connection Count */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Wifi className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Connections
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.connections?.length || 0}
                  </dd>
                  <dd className="text-xs text-orange-600">
                    Active
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Network Information */}
      <div className="bg-white overflow-hidden shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Network Information</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <dt className="text-sm font-medium text-gray-500">Local IP</dt>
              <dd className="mt-1 text-sm text-gray-900">*************</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Public IP</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {isConnected ? "************" : "Not connected"}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">VPN Server</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {isConnected ? 
                  nodes.find(n => n.id === selectedNode)?.location || "Direct Connection"
                  : "Not connected"}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Protocol</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {isConnected ? "Shadowsocks/Trojan" : "N/A"}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Encryption</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {isConnected ? "ChaCha20Poly1305" : "N/A"}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Proxy Mode</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {proxyModes.find(m => m.id === proxyMode)?.name || "Global Mode"}
              </dd>
            </div>
          </div>
          
          {/* Connection History/Status */}
          {isConnected && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Connection Details</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div>Connected via: {nodes.find(n => n.id === selectedNode)?.name || "Direct"}</div>
                <div>Current latency: {nodes.find(n => n.id === selectedNode)?.latency || 0}ms</div>
                <div>Data encrypted: Yes</div>
                <div>DNS protected: Yes</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;