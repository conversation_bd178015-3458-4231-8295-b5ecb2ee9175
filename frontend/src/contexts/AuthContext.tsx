import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { invoke } from "@tauri-apps/api/core";

interface User {
  id: string;
  username: string;
  email: string;
  role: string;
}

interface AuthData {
  user: User;
  token_info: {
    access_token: string;
    refresh_token: string;
    expires_at: string;
    refresh_expires_at: string;
  };
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  isLoading: boolean;
  refreshToken: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored authentication data on startup
    const checkAuthStatus = async () => {
      try {
        const isAuthenticated = await invoke<boolean>("auth_is_authenticated");
        
        if (isAuthenticated) {
          const currentUser = await invoke<User | null>("auth_get_current_user");
          if (currentUser) {
            setUser(currentUser);
            // We don't store the token in state, but we know user is authenticated
            setToken("authenticated");
          }
        }
      } catch (error) {
        console.error("Failed to check authentication status:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      const authData = await invoke<AuthData>("auth_login", { username, password });
      
      setUser(authData.user);
      setToken("authenticated");
      
      return true;
    } catch (error) {
      console.error("Login error:", error);
      return false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await invoke("auth_logout");
      setUser(null);
      setToken(null);
    } catch (error) {
      console.error("Logout error:", error);
      // Even if logout fails, clear local state
      setUser(null);
      setToken(null);
    }
  };

  const refreshToken = async (): Promise<boolean> => {
    try {
      const authData = await invoke<AuthData>("auth_refresh_token");
      setUser(authData.user);
      setToken("authenticated");
      return true;
    } catch (error) {
      console.error("Token refresh error:", error);
      // If refresh fails, clear authentication
      setUser(null);
      setToken(null);
      return false;
    }
  };

  const value: AuthContextType = {
    user,
    token,
    login,
    logout,
    isAuthenticated: !!user && !!token,
    isLoading,
    refreshToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};