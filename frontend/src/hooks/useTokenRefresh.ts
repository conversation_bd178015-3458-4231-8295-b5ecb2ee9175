import { useEffect, useRef } from "react";
import { useAuth } from "../contexts/AuthContext";

export const useTokenRefresh = () => {
  const { refreshToken, isAuthenticated } = useAuth();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      // Clear any existing interval if user is not authenticated
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Set up automatic token refresh every 30 minutes
    const refreshInterval = 30 * 60 * 1000; // 30 minutes
    
    intervalRef.current = setInterval(async () => {
      try {
        await refreshToken();
        console.log("Token refreshed successfully");
      } catch (error) {
        console.error("Failed to refresh token:", error);
        // Token refresh failed, user will be logged out by the AuthContext
      }
    }, refreshInterval);

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [isAuthenticated, refreshToken]);

  // Also provide a manual refresh function
  const manualRefresh = async () => {
    try {
      const success = await refreshToken();
      return success;
    } catch (error) {
      console.error("Manual token refresh failed:", error);
      return false;
    }
  };

  return { manualRefresh };
};