[package]
name = "client"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true

[build-dependencies]
tauri-build.workspace = true

[dependencies]
# Workspace dependencies
serde.workspace = true
serde_json.workspace = true
tokio.workspace = true
anyhow.workspace = true
thiserror.workspace = true
tracing.workspace = true
tracing-subscriber = { workspace = true, features = ["env-filter"] }
uuid.workspace = true
chrono.workspace = true

# Client specific
tauri.workspace = true
reqwest.workspace = true
leaf = { path = "../../leaf/leaf", default-features = false, features = ["config-json", "inbound-shadowsocks", "outbound-private-tun", "outbound-direct", "inbound-tun", "api", "stat", "aws-lc-aead", "rustls-tls-aws-lc", "rustls-tls", "outbound-select", "no-tracing"] }
privilege = "0.3"

# Local dependencies
shared = { path = "../shared" }

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]