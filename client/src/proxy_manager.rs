use anyhow::{anyhow, Result};
use serde_json::{json, Value};
use std::sync::atomic::{AtomicU16, Ordering};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tracing::{error, info, warn};

static RUNTIME_COUNTER: AtomicU16 = AtomicU16::new(1);

pub struct ProxyManager {
    status: ProxyStatus,
    config_content: Option<String>,
    start_time: Option<u64>,
    runtime_id: Option<leaf::RuntimeId>,
    stat_manager: Option<Arc<tokio::sync::RwLock<leaf::app::stat_manager::StatManager>>>,
    api_base_url: String,
}

#[derive(Debug, Clone)]
enum ProxyStatus {
    Disconnected,
    Connecting,
    Connected,
    Error(String),
}

impl ProxyManager {
    pub fn new() -> Self {
        Self {
            status: ProxyStatus::Disconnected,
            config_content: None,
            start_time: None,
            runtime_id: None,
            stat_manager: None,
            api_base_url: "http://127.0.0.1:9090".to_string(),
        }
    }

    pub fn get_status(&self) -> String {
        match &self.status {
            ProxyStatus::Disconnected => "disconnected".to_string(),
            ProxyStatus::Connecting => "connecting".to_string(),
            ProxyStatus::Connected => "connected".to_string(),
            ProxyStatus::Error(msg) => format!("error: {}", msg),
        }
    }

    pub async fn start(&mut self) -> Result<(), String> {
        if matches!(self.status, ProxyStatus::Connected) {
            return Ok(()); // Already running
        }

        if self.config_content.is_none() {
            return Err("No configuration available".to_string());
        }

        self.status = ProxyStatus::Connecting;
        info!("Starting leaf proxy engine...");

        // Set API_LISTEN environment variable for leaf API server
        std::env::set_var("API_LISTEN", "127.0.0.1:9090");

        let config_content = self.config_content.as_ref().unwrap().clone();
        let runtime_id = RUNTIME_COUNTER.fetch_add(1, Ordering::SeqCst);

        // Parse JSON config and convert to leaf internal config
        let config = match leaf::config::json::from_string(&config_content) {
            Ok(cfg) => cfg,
            Err(e) => {
                let error_msg = format!("Invalid proxy configuration: {}", e);
                error!("{}", error_msg);
                self.status = ProxyStatus::Error(error_msg.clone());
                return Err(error_msg);
            }
        };

        // Start leaf proxy in a separate thread to avoid blocking
        let start_options = leaf::StartOptions {
            config: leaf::Config::Internal(config),
            #[cfg(feature = "auto-reload")]
            auto_reload: false,
            runtime_opt: leaf::RuntimeOption::MultiThreadAuto(2 * 1024 * 1024), // 2MB stack
        };

        // Use tokio spawn to run leaf::start in a separate task
        // leaf::start blocks until shutdown, so we don't wait for it
        let rt_id = runtime_id;
        let task = std::thread::spawn(move || leaf::start(rt_id, start_options));
        // if not timeout, start failed, because leaf::start blocks until shutdown
        // if !start_result.is_err() {
        //     // start failed
        //     let error_msg = "Leaf proxy failed to start properly".to_string();
        //     error!("{}", error_msg);
        //     self.status = ProxyStatus::Error(error_msg.clone());
        //     return Err(error_msg);
        // }
        tokio::time::sleep(Duration::from_secs(5)).await;
        // Check if the runtime is actually running
        if leaf::is_running(runtime_id) {
            self.status = ProxyStatus::Connected;
            self.runtime_id = Some(runtime_id);
            self.start_time = Some(
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            );

            // Get stat manager for traffic monitoring
            if let Some(manager) = leaf::RUNTIME_MANAGER.lock().unwrap().get(&runtime_id) {
                self.stat_manager = Some(manager.stat_manager());
            }

            info!(
                "Leaf proxy started successfully with runtime ID: {}",
                runtime_id
            );
            Ok(())
        } else {
            let error_msg = "Leaf proxy failed to start properly".to_string();
            error!("{}", error_msg);
            self.status = ProxyStatus::Error(error_msg.clone());
            Err(error_msg)
        }
    }

    pub async fn stop(&mut self) -> Result<(), String> {
        if matches!(self.status, ProxyStatus::Disconnected) {
            return Ok(()); // Already stopped
        }

        if let Some(runtime_id) = self.runtime_id.take() {
            info!("Stopping leaf proxy with runtime ID: {}", runtime_id);

            std::thread::spawn(move || {
                leaf::shutdown(runtime_id);
            });
        }

        self.status = ProxyStatus::Disconnected;
        self.start_time = None;
        self.stat_manager = None;

        Ok(())
    }

    pub async fn update_config(&mut self, config: String) -> Result<String, String> {
        // Stop current runner if running
        if matches!(self.status, ProxyStatus::Connected) {
            self.stop().await?;
        }

        // Update config
        self.config_content = Some(config);
        Ok("Configuration updated".to_string())
    }

    pub fn get_stats(&self) -> Value {
        if matches!(self.status, ProxyStatus::Connected) {
            // Try to get real stats from leaf if available
            if let Some(stat_manager) = &self.stat_manager {
                // For now, we'll return basic stats since accessing the stat manager
                // requires async operations and this is a sync function
                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();

                if let Some(start_time) = self.start_time {
                    let elapsed = now - start_time;

                    return json!({
                        "status": self.get_status(),
                        "uptime": elapsed,
                        "upload_bytes": 0, // TODO: Get from leaf stat manager
                        "download_bytes": 0, // TODO: Get from leaf stat manager
                        "upload_speed": 0,
                        "download_speed": 0,
                        "connections": []
                    });
                }
            } else {
                // Fallback to basic status
                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();

                if let Some(start_time) = self.start_time {
                    let elapsed = now - start_time;

                    return json!({
                        "status": self.get_status(),
                        "uptime": elapsed,
                        "upload_bytes": 0,
                        "download_bytes": 0,
                        "upload_speed": 0,
                        "download_speed": 0,
                        "connections": []
                    });
                }
            }
        }

        json!({
            "status": self.get_status(),
            "uptime": 0,
            "upload_bytes": 0,
            "download_bytes": 0,
            "upload_speed": 0,
            "download_speed": 0,
            "connections": []
        })
    }

    /// Get detailed proxy statistics (async version for accessing leaf stats)
    pub async fn get_detailed_stats(&self) -> Value {
        if matches!(self.status, ProxyStatus::Connected) {
            if let Some(stat_manager) = &self.stat_manager {
                // Access the stat manager to get real traffic data
                let stats = stat_manager.read().await;

                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();

                let uptime = if let Some(start_time) = self.start_time {
                    now - start_time
                } else {
                    0
                };

                // Calculate total stats from all counters
                let total_upload_bytes: u64 = stats.counters.iter().map(|c| c.bytes_sent()).sum();
                let total_download_bytes: u64 =
                    stats.counters.iter().map(|c| c.bytes_recvd()).sum();
                let active_connections = stats
                    .counters
                    .iter()
                    .filter(|c| !c.recv_completed() && !c.send_completed())
                    .count();

                return json!({
                    "status": self.get_status(),
                    "uptime": uptime,
                    "upload_bytes": total_upload_bytes,
                    "download_bytes": total_download_bytes,
                    "upload_speed": 0, // TODO: Calculate speed from byte differences over time
                    "download_speed": 0, // TODO: Calculate speed from byte differences over time
                    "connections": active_connections
                });
            }
        }

        self.get_stats()
    }

    /// Get available proxy nodes from leaf API
    pub async fn get_proxy_nodes(&self, outbound: &str) -> Result<Vec<String>, String> {
        if !matches!(self.status, ProxyStatus::Connected) {
            return Err("Proxy is not connected".to_string());
        }

        let url = format!("{}/api/v1/app/outbound/selects?outbound={}", self.api_base_url, outbound);
        
        match reqwest::get(&url).await {
            Ok(response) => {
                if response.status().is_success() {
                    match response.json::<Vec<String>>().await {
                        Ok(nodes) => Ok(nodes),
                        Err(e) => Err(format!("Failed to parse nodes list: {}", e)),
                    }
                } else {
                    Err(format!("API request failed with status: {}", response.status()))
                }
            }
            Err(e) => Err(format!("Failed to request nodes: {}", e)),
        }
    }

    /// Get currently selected proxy node
    pub async fn get_selected_node(&self, outbound: &str) -> Result<Option<String>, String> {
        if !matches!(self.status, ProxyStatus::Connected) {
            return Err("Proxy is not connected".to_string());
        }

        let url = format!("{}/api/v1/app/outbound/select?outbound={}", self.api_base_url, outbound);
        
        match reqwest::get(&url).await {
            Ok(response) => {
                if response.status().is_success() {
                    match response.json::<serde_json::Value>().await {
                        Ok(data) => {
                            if let Some(selected) = data.get("selected") {
                                Ok(selected.as_str().map(|s| s.to_string()))
                            } else {
                                Ok(None)
                            }
                        }
                        Err(e) => Err(format!("Failed to parse selected node: {}", e)),
                    }
                } else {
                    Err(format!("API request failed with status: {}", response.status()))
                }
            }
            Err(e) => Err(format!("Failed to request selected node: {}", e)),
        }
    }

    /// Set proxy node selection
    pub async fn set_selected_node(&self, outbound: &str, select: &str) -> Result<(), String> {
        if !matches!(self.status, ProxyStatus::Connected) {
            return Err("Proxy is not connected".to_string());
        }

        let url = format!("{}/api/v1/app/outbound/select?outbound={}&select={}", 
                         self.api_base_url, outbound, select);
        
        let client = reqwest::Client::new();
        match client.post(&url).send().await {
            Ok(response) => {
                if response.status().is_success() {
                    Ok(())
                } else {
                    Err(format!("Failed to set node selection: {}", response.status()))
                }
            }
            Err(e) => Err(format!("Failed to set node selection: {}", e)),
        }
    }
}

impl Default for ProxyManager {
    fn default() -> Self {
        Self::new()
    }
}
