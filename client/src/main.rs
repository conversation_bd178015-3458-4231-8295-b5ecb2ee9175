// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::Arc;
use tauri::{Manager, State};
use tokio::sync::Mutex;

mod auth_manager;
mod log_manager;
mod proxy_manager;
use auth_manager::{AuthData, AuthManager, User};
use log_manager::{LogEntry, LogManager};
use proxy_manager::ProxyManager;

struct AppState {
    proxy_manager: Arc<Mutex<ProxyManager>>,
    auth_manager: Arc<AuthManager>,
    log_manager: Arc<LogManager>,
}

fn main() {
    #[cfg(not(target_os = "android"))]
    if !check_sudo() {
        use std::process;
        process::exit(0);
    }
    let proxy_manager = Arc::new(Mutex::new(ProxyManager::new()));
    let auth_manager = Arc::new(AuthManager::new("http://127.0.0.1:3000".to_string()));
    let log_manager = Arc::new(LogManager::new(1000)); // Store up to 1000 log entries

    // Initialize logging with log collection
    if let Err(e) = log_manager::init_logging_with_collection(&log_manager) {
        eprintln!("Failed to initialize logging: {}", e);
    }

    tauri::Builder::default()
        .manage(AppState {
            proxy_manager,
            auth_manager,
            log_manager,
        })
        .setup(|app| {
            #[cfg(debug_assertions)]
            {
                let window = app.get_webview_window("main").unwrap();
                window.open_devtools();
            }

            // Load saved authentication data on startup
            let app_handle = app.handle().clone();
            let auth_manager = app.state::<AppState>().auth_manager.clone();

            tauri::async_runtime::spawn(async move {
                if let Err(e) = auth_manager.load_from_secure_storage(&app_handle).await {
                    eprintln!("Failed to load authentication data: {}", e);
                }
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet,
            get_proxy_status,
            toggle_proxy,
            get_proxy_stats,
            update_config,
            auth_login,
            auth_logout,
            auth_get_current_user,
            auth_is_authenticated,
            auth_refresh_token,
            get_vpn_config,
            set_vpn_config,
            get_proxy_nodes,
            select_proxy_node,
            get_selected_proxy_node,
            get_logs,
            clear_logs
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn get_proxy_status(state: State<'_, AppState>) -> Result<String, String> {
    let proxy_manager = state.proxy_manager.lock().await;
    Ok(proxy_manager.get_status())
}

#[tauri::command]
async fn toggle_proxy(enabled: bool, state: State<'_, AppState>) -> Result<String, String> {
    let mut proxy_manager = state.proxy_manager.lock().await;

    if enabled {
        proxy_manager.start().await?;
    } else {
        proxy_manager.stop().await?;
    }

    Ok(proxy_manager.get_status())
}

#[tauri::command]
async fn get_proxy_stats(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    let proxy_manager = state.proxy_manager.lock().await;
    Ok(proxy_manager.get_detailed_stats().await)
}

#[tauri::command]
async fn update_config(config: String, state: State<'_, AppState>) -> Result<String, String> {
    let mut proxy_manager = state.proxy_manager.lock().await;
    proxy_manager.update_config(config).await
}

// Authentication commands
#[tauri::command]
async fn auth_login(
    username: String,
    password: String,
    state: State<'_, AppState>,
    _app_handle: tauri::AppHandle,
) -> Result<AuthData, String> {
    let auth_manager = &state.auth_manager;

    match auth_manager.login(username, password).await {
        Ok(auth_data) => {
            // Save authentication data securely
            if let Err(e) = auth_manager.save_to_secure_storage(&_app_handle).await {
                eprintln!("Failed to save authentication data: {}", e);
            }
            Ok(auth_data)
        }
        Err(e) => Err(format!("Login failed: {}", e)),
    }
}

#[tauri::command]
async fn auth_logout(
    state: State<'_, AppState>,
    _app_handle: tauri::AppHandle,
) -> Result<(), String> {
    let auth_manager = &state.auth_manager;

    match auth_manager.logout().await {
        Ok(()) => {
            // Clear stored authentication data
            if let Err(e) = auth_manager.clear_secure_storage().await {
                eprintln!("Failed to clear authentication data: {}", e);
            }
            Ok(())
        }
        Err(e) => Err(format!("Logout failed: {}", e)),
    }
}

#[tauri::command]
async fn auth_get_current_user(state: State<'_, AppState>) -> Result<Option<User>, String> {
    let auth_manager = &state.auth_manager;
    Ok(auth_manager.get_current_user().await)
}

#[tauri::command]
async fn auth_is_authenticated(state: State<'_, AppState>) -> Result<bool, String> {
    let auth_manager = &state.auth_manager;
    Ok(auth_manager.is_authenticated().await)
}

#[tauri::command]
async fn auth_refresh_token(
    state: State<'_, AppState>,
    _app_handle: tauri::AppHandle,
) -> Result<AuthData, String> {
    let auth_manager = &state.auth_manager;

    match auth_manager.refresh_token().await {
        Ok(auth_data) => {
            // Save updated authentication data
            if let Err(e) = auth_manager.save_to_secure_storage(&_app_handle).await {
                eprintln!("Failed to save refreshed authentication data: {}", e);
            }
            Ok(auth_data)
        }
        Err(e) => Err(format!("Token refresh failed: {}", e)),
    }
}

// VPN Configuration Management Commands
#[tauri::command]
async fn get_vpn_config(
    state: State<'_, AppState>,
    _app_handle: tauri::AppHandle,
) -> Result<Option<String>, String> {
    let auth_manager = &state.auth_manager;

    // Check if user is authenticated
    if !auth_manager.is_authenticated().await {
        return Err("User not authenticated".to_string());
    }

    // Get user's VPN configuration from backend
    if let Some(token) = auth_manager.get_access_token().await {
        let client = reqwest::Client::new();
        let response = client
            .get("http://127.0.0.1:3000/api/user/config")
            .header("Authorization", format!("Bearer {}", token))
            .send()
            .await
            .map_err(|e| format!("Failed to fetch config: {}", e))?;

        if response.status().is_success() {
            let config_text = response
                .text()
                .await
                .map_err(|e| format!("Failed to read config: {}", e))?;
            Ok(Some(config_text))
        } else {
            Err(format!("Failed to get config: {}", response.status()))
        }
    } else {
        Err("No access token available".to_string())
    }
}

#[tauri::command]
async fn set_vpn_config(config: String, state: State<'_, AppState>) -> Result<String, String> {
    let mut proxy_manager = state.proxy_manager.lock().await;
    proxy_manager.update_config(config).await
}

#[cfg(not(target_os = "android"))]
fn check_sudo() -> bool {
    use std::env::current_exe;
    let is_elevated = privilege::user::privileged();
    if !is_elevated {
        let Ok(exe) = current_exe() else {
            return true;
        };
        let mut elevated_cmd = privilege::runas::Command::new(exe);
        let _ = elevated_cmd.force_prompt(true).hide(true).gui(true).run();
    }
    is_elevated
}

// Proxy Node Management Commands
#[tauri::command]
async fn get_proxy_nodes(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    let auth_manager = &state.auth_manager;

    // Check if user is authenticated
    if !auth_manager.is_authenticated().await {
        return Err("User not authenticated".to_string());
    }

    // Get proxy manager for accessing leaf API
    let proxy_manager = &state.proxy_manager.lock().await;

    // Get available nodes from leaf API
    let nodes_list = match proxy_manager.get_proxy_nodes("Proxy").await {
        Ok(nodes) => nodes,
        Err(_) => {
            // Fallback to mock data if API not available
            vec!["direct".to_string(), "fallback-node".to_string()]
        }
    };

    // Get currently selected node
    let selected_node = proxy_manager
        .get_selected_node("Proxy")
        .await
        .unwrap_or(None);

    // Format response for frontend
    let nodes = serde_json::json!({
        "nodes": nodes_list.iter().map(|node| {
            serde_json::json!({
                "id": node,
                "name": node,
                "location": "Unknown",
                "latency": 0,
                "available": true
            })
        }).collect::<Vec<_>>(),
        "selected": selected_node.unwrap_or_else(|| "direct".to_string())
    });

    Ok(nodes)
}

#[tauri::command]
async fn select_proxy_node(node_id: String, state: State<'_, AppState>) -> Result<String, String> {
    let proxy_manager = state.proxy_manager.lock().await;

    // Set selected node via leaf API
    match proxy_manager.set_selected_node("Proxy", &node_id).await {
        Ok(()) => Ok(format!("Selected node: {}", node_id)),
        Err(e) => Err(format!("Failed to select node: {}", e)),
    }
}

#[tauri::command]
async fn get_selected_proxy_node(state: State<'_, AppState>) -> Result<String, String> {
    let proxy_manager = state.proxy_manager.lock().await;

    // Get currently selected node from leaf API
    match proxy_manager.get_selected_node("Proxy").await {
        Ok(Some(node)) => Ok(node),
        Ok(None) => Ok("direct".to_string()), // default fallback
        Err(e) => Err(format!("Failed to get selected node: {}", e)),
    }
}

// Log management commands
#[tauri::command]
async fn get_logs(
    level_filter: Option<String>,
    limit: Option<usize>,
    state: State<'_, AppState>,
) -> Result<Vec<LogEntry>, String> {
    let log_manager = &state.log_manager;

    let level_filter_ref = level_filter.as_deref();
    let logs = log_manager.get_logs_filtered(level_filter_ref, limit);

    Ok(logs)
}

#[tauri::command]
fn clear_logs(state: State<'_, AppState>) -> Result<String, String> {
    let log_manager = &state.log_manager;
    log_manager.clear_logs();
    Ok("Logs cleared successfully".to_string())
}
